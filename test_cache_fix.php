<?php
/**
 * Test script to verify the scheduled course counter cache fix
 * 
 * This script tests:
 * 1. Creating a schedule and verifying cache is cleared
 * 2. Checking that the counter updates immediately
 * 3. Activating a schedule and verifying cache is cleared
 * 4. Deleting a schedule and verifying cache is cleared
 */

// WordPress environment setup
require_once('wp-config.php');
require_once(ABSPATH . 'wp-admin/includes/admin.php');

// Load LearnPressium classes
require_once('learnpressium/includes/modules/enrollment/class-simple-enrollment-manager.php');
require_once('learnpressium/includes/modules/enrollment/class-enrollment-profile-integration.php');

echo "<h1>LearnPressium Scheduled Course Counter Cache Fix Test</h1>\n";

// Test user and course IDs (adjust these to match your test environment)
$test_user_id = 1; // Change to a valid user ID
$test_course_id = 123; // Change to a valid course ID

echo "<h2>Test Configuration</h2>\n";
echo "Test User ID: {$test_user_id}<br>\n";
echo "Test Course ID: {$test_course_id}<br>\n";

// Initialize managers
$simple_manager = new Simple_Enrollment_Manager();
$profile_integration = new Enrollment_Profile_Integration();

echo "<h2>Test 1: Initial Counter Check</h2>\n";

// Check initial count
$initial_count = $simple_manager->count_user_scheduled_courses($test_user_id);
echo "Initial scheduled course count: {$initial_count}<br>\n";

// Check cache key
$cache_key = 'learnpressium_scheduled_count_' . $test_user_id;
$cached_count = get_transient($cache_key);
echo "Cached count: " . ($cached_count !== false ? $cached_count : 'Not cached') . "<br>\n";

echo "<h2>Test 2: Create Schedule and Check Cache Clearing</h2>\n";

// Create a test schedule
$start_date = date('Y-m-d H:i:s', strtotime('+1 day'));
$end_date = date('Y-m-d H:i:s', strtotime('+30 days'));

echo "Creating schedule for user {$test_user_id}, course {$test_course_id}<br>\n";
echo "Start date: {$start_date}<br>\n";

$schedule_result = $simple_manager->schedule_enrollment($test_user_id, $test_course_id, $start_date, $end_date, 'Test schedule');

if ($schedule_result) {
    echo "✅ Schedule created successfully with ID: {$schedule_result}<br>\n";
    
    // Check if cache was cleared
    $cached_count_after = get_transient($cache_key);
    echo "Cached count after creation: " . ($cached_count_after !== false ? $cached_count_after : 'Cache cleared ✅') . "<br>\n";
    
    // Check new count
    $new_count = $simple_manager->count_user_scheduled_courses($test_user_id);
    echo "New scheduled course count: {$new_count}<br>\n";
    
    if ($new_count > $initial_count) {
        echo "✅ Counter increased correctly<br>\n";
    } else {
        echo "❌ Counter did not increase<br>\n";
    }
} else {
    echo "❌ Failed to create schedule<br>\n";
}

echo "<h2>Test 3: Profile Integration Counter</h2>\n";

// Simulate profile statistics request
$_REQUEST['userID'] = $test_user_id;
$test_data = array();
$profile_data = $profile_integration->add_scheduled_courses_to_statistics($test_data);

if (isset($profile_data['scheduled_courses'])) {
    $profile_count = $profile_data['scheduled_courses']['count'];
    echo "Profile integration count: {$profile_count}<br>\n";
    
    if ($profile_count == $new_count) {
        echo "✅ Profile integration shows correct count<br>\n";
    } else {
        echo "❌ Profile integration count mismatch<br>\n";
    }
} else {
    echo "❌ Profile integration did not add scheduled courses data<br>\n";
}

echo "<h2>Test 4: Cache Behavior Test</h2>\n";

// Set a manual cache value to test clearing
set_transient($cache_key, 999, 300);
echo "Set manual cache value to 999<br>\n";

$cached_manual = get_transient($cache_key);
echo "Manual cached value: {$cached_manual}<br>\n";

// Clear cache using our method
$simple_manager->clear_user_scheduled_cache($test_user_id);
echo "Called clear_user_scheduled_cache()<br>\n";

$cached_after_clear = get_transient($cache_key);
echo "Cached value after clear: " . ($cached_after_clear !== false ? $cached_after_clear : 'Cache cleared ✅') . "<br>\n";

echo "<h2>Test 5: Database Verification</h2>\n";

// Check database directly
global $wpdb;
$scheduled_table = $wpdb->prefix . 'learnpressium_enrollment_schedules';
$db_count = $wpdb->get_var($wpdb->prepare(
    "SELECT COUNT(*) FROM {$scheduled_table} WHERE user_id = %d AND status = 'pending'",
    $test_user_id
));

echo "Database count: {$db_count}<br>\n";
echo "Manager count: " . $simple_manager->count_user_scheduled_courses($test_user_id) . "<br>\n";

if ($db_count == $simple_manager->count_user_scheduled_courses($test_user_id)) {
    echo "✅ Database and manager counts match<br>\n";
} else {
    echo "❌ Database and manager counts don't match<br>\n";
}

echo "<h2>Test 6: Cleanup</h2>\n";

// Clean up test data
if ($schedule_result) {
    $delete_result = $wpdb->delete(
        $scheduled_table,
        array('schedule_id' => $schedule_result),
        array('%d')
    );
    
    if ($delete_result) {
        echo "✅ Test schedule cleaned up<br>\n";
        
        // Verify cache is cleared after deletion
        $simple_manager->clear_user_scheduled_cache($test_user_id);
        $final_count = $simple_manager->count_user_scheduled_courses($test_user_id);
        echo "Final count after cleanup: {$final_count}<br>\n";
    } else {
        echo "❌ Failed to clean up test schedule<br>\n";
    }
}

echo "<h2>Test Summary</h2>\n";
echo "✅ = Test passed<br>\n";
echo "❌ = Test failed<br>\n";
echo "<br>If all tests show ✅, the cache fix is working correctly!<br>\n";

?>
